<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .coffee-bg { fill: #8B6F47; }
      .coffee-accent { fill: #FDF6E3; }
      .coffee-text { fill: #FFFFFF; font-family: 'Inter', sans-serif; font-weight: 600; font-size: 8px; }
    </style>
  </defs>
  
  <!-- Background circle with gradient -->
  <defs>
    <radialGradient id="bgGradient" cx="0.3" cy="0.3">
      <stop offset="0%" stop-color="#A0845C"/>
      <stop offset="100%" stop-color="#8B6F47"/>
    </radialGradient>
  </defs>
  <circle cx="16" cy="16" r="15" fill="url(#bgGradient)" stroke="#6B5B3A" stroke-width="1"/>

  <!-- Large Coffee cup icon -->
  <g transform="translate(6, 4)">
    <!-- Cup body -->
    <path d="M3 8 L3 20 Q3 22 5 22 L15 22 Q17 22 17 20 L17 8 Z" class="coffee-accent" stroke="#8B6F47" stroke-width="0.5"/>
    <!-- Cup handle -->
    <path d="M17 10 Q20 10 20 13 Q20 16 17 16" fill="none" stroke="#FDF6E3" stroke-width="2"/>
    <!-- Coffee surface -->
    <ellipse cx="10" cy="8" rx="7" ry="1.5" class="coffee-bg"/>
    <!-- Steam lines -->
    <path d="M7 5 Q7 2 8 2 Q9 5 9 2" fill="none" stroke="#FDF6E3" stroke-width="1.5" opacity="0.8"/>
    <path d="M11 5 Q11 2 12 2 Q13 5 13 2" fill="none" stroke="#FDF6E3" stroke-width="1.5" opacity="0.8"/>
  </g>
</svg>